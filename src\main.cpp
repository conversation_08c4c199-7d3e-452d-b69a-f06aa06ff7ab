#include <Arduino.h>
#include <lib/dshot/esc.h>
#include <lib/HX711.h>

const uint8_t DSHOT_PIN = 27;
const uint8_t HX711_DATA_PIN = 28;  // Can use any pins!
const uint8_t HX711_CLOCK_PIN = 29; // Can use any pins!

DShot::ESC dshot(DSHOT_PIN, pio0, DShot::Type::Bidir, DShot::Speed::DS300, 14);
HX711 hx711(HX711_DATA_PIN, HX711_CLOCK_PIN, CHAN_A_GAIN_128, 700);

void setup()
{
    Serial.begin(115200);

    // wait for serial port to connect. Needed for native USB port only
    // while (!Serial) {
    //     delay(10);
    // }

    dshot.init();
    hx711.init(); // This now automatically sets up the interrupt
}

// the loop routine runs over and over again forever:
// todo: remove telemetry from here, include in dshot class
DShot::Telemetry telemetry = {0};
void loop()
{
    delay(2);
    // Serial.print("dshot: ");
    // print_bin(dshot.setCommand(1046));
    if (millis() < 3000) {
        dshot.setCommand(0); // 1046 is the example command
        Serial.println("Sending command 0");
    }
    else if (millis() < 4000)
    {
        dshot.setCommand(13); // extended telemetry enable
        Serial.println("Sending command 13");
    }
    else
    {
        dshot.setThrottle(0.10); // https://github.com/betaflight/betaflight/issues/2879
        Serial.println("Sending throttle");
    }
    delay(1);                    // wait for dshot PIO to be done
    uint64_t raw_telemetry;
    if (dshot.getRawTelemetry(raw_telemetry))
    {
        // Serial.print("Tel: ");
        // print_bin(raw_telemetry);
        dshot.decodeTelemetry(raw_telemetry, telemetry);
        Serial.print(telemetry.rpm);
        Serial.print(", ");
        Serial.print(telemetry.temperature_C);
        Serial.print(", ");
        Serial.print(telemetry.volts_cV / 100);
        Serial.print(".");
        Serial.print(telemetry.volts_cV % 100);
        Serial.print(", ");
        Serial.print(telemetry.amps_A);
        Serial.print(", ");
        Serial.print(telemetry.errors);
        Serial.print("/");
        Serial.print(telemetry.reads);
        Serial.println("");
        if (telemetry.reads % 1000 == 0)
        {
            telemetry.errors = 0;
            telemetry.reads = 0;
        }
    }
    else
    {
        Serial.println("No telemetry :(");
    }

    // Print weight data only if we have recent data and enough time has passed
    if (millis() > 10000 && hx711.getLastReadTime() > 0)
    {
        Serial.print("Weight: ");
        Serial.print(hx711.getLatestWeight());
        Serial.println("g");
    }

}
