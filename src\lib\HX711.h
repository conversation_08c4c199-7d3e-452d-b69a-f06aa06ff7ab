#include "Arduino.h"

enum hx711_chanGain_t
{
    CHAN_A_GAIN_128 = 25,
    CHAN_A_GAIN_64 = 27,
    CHAN_B_GAIN_32 = 26
};

// Forward declaration for interrupt handling
class HX711;

// Global pointer for interrupt handling (only one HX711 instance supported)
extern HX711* g_hx711_instance;

class HX711
{

public:
    HX711(uint8_t dataPin, uint8_t clockPin, hx711_chanGain_t gain, double scaleToGramms = 700)
    {
        this->dataPin = dataPin;
        this->clockPin = clockPin;
        this->gain = gain;
        this->scaleToGramms = scaleToGramms;
        this->currentWeight = 0.0;
        this->weightReady = false;
        this->lastWeightRead = 0;

        // Set global instance for interrupt handling
        g_hx711_instance = this;
    }

    void init()
    {
        pinMode(dataPin, INPUT_PULLUP);
        pinMode(clockPin, OUTPUT);
        powerDown(true);  // Perform a power reset
        delay(1);         // Hold pin high for 1 ms for reset
        powerDown(false); // Wake up
        tare();

        // Setup interrupt for data ready (falling edge on data pin)
        attachInterrupt(digitalPinToInterrupt(dataPin), hx711_data_ready_isr, FALLING);
    }

    /**
     * @brief Enable or disable interrupt-based reading
     *
     * @param enable true to enable interrupt reading, false to disable
     */
    void enableInterruptReading(bool enable = true)
    {
        if (enable) {
            attachInterrupt(digitalPinToInterrupt(dataPin), hx711_data_ready_isr, FALLING);
        } else {
            detachInterrupt(digitalPinToInterrupt(dataPin));
        }
    }

    void tare()
    {
        // read and toss 3 values each
        for (uint8_t t = 0; t < 3; t++)
        {
            tare(readChannelRaw());
        }
    }

    /**
     * @brief Get weight using blocking read (original method)
     *
     * @return float Weight in grams
     */
    float getWeight()
    {
        return (readChannelRaw() - tareValue) / scaleToGramms;
    }

    /**
     * @brief Get the latest weight from interrupt-based reading
     *
     * @return float Latest weight in grams (non-blocking)
     */
    float getLatestWeight()
    {
        return currentWeight;
    }

    /**
     * @brief Check if new weight data is available from interrupt
     *
     * @return true if new weight data is ready
     */
    bool isWeightReady()
    {
        return weightReady;
    }

    /**
     * @brief Get the latest weight and clear the ready flag
     *
     * @return float Latest weight in grams
     */
    float getWeightAndClear()
    {
        weightReady = false;
        return currentWeight;
    }

    /**
     * @brief Get timestamp of last weight reading
     *
     * @return unsigned long Timestamp in milliseconds
     */
    unsigned long getLastReadTime()
    {
        return lastWeightRead;
    }

    /**
     * @brief Check if the HX711 is busy
     *
     * @return true If the HX711 is currently busy (DOUT is high)
     * @return false If the HX711 is ready for data retrieval (DOUT is low)
     */
    bool isBusy()
    {
        return digitalRead(dataPin) == HIGH;
    }

    void tare(int32_t tareValue)
    {
        this->tareValue = tareValue;
    }

    /**
     * @brief Internal method called by interrupt to handle new weight data
     * This should only be called from the interrupt service routine
     */
    void handleInterrupt()
    {
        if (!isBusy()) {
            // Read weight in interrupt context (keep it fast)
            currentWeight = (readChannelRaw() - tareValue) / scaleToGramms;
            lastWeightRead = millis();
            weightReady = true;
        }
    }

private:
    uint8_t dataPin;
    uint8_t clockPin;
    hx711_chanGain_t gain;
    double scaleToGramms;
    int32_t tareValue; ///< Tare offset value

    // Interrupt-related variables
    volatile float currentWeight;      ///< Latest weight reading from interrupt
    volatile bool weightReady;        ///< Flag indicating new weight data is available
    volatile unsigned long lastWeightRead; ///< Timestamp of last weight reading

    /**
     * @brief Power down or wake up the HX711
     *
     * @param down true to power down, false to wake up
     */
    void powerDown(bool down)
    {
        digitalWrite(clockPin, down ? HIGH : LOW);
    }

    /**
     * @brief Read data from the HX711, handling channel and gain setup,
     * NO tare offset - the 'raw' ADC value from the HX!
     *
     * Read is blocking this will not be more than 10 or 80 SPS (L or H switch)
     *
     * @return int32_t The signed 32-bit extended raw sensor data
     */
    int32_t readChannelRaw()
    {
        while (isBusy())
            ; // Wait until the HX711 is ready

        digitalWrite(clockPin, LOW);
        uint32_t value = 0;
        for (int i = 0; i < 24; i++)
        { // Read 24 bits from DOUT
            digitalWrite(clockPin, HIGH);
            delayMicroseconds(1);
            value = (value << 1) | digitalRead(dataPin);
            digitalWrite(clockPin, LOW);
            delayMicroseconds(1);
        }

        // Set gain for next reading
        for (int i = 0; i < gain - 24; i++)
        {
            digitalWrite(clockPin, HIGH);
            digitalWrite(clockPin, LOW);
        }

        // Convert to 32-bit signed integer
        if (value & 0x800000)
            value |= 0xFF000000;

        return (int32_t)value;
    }
};

// Global interrupt service routine
void hx711_data_ready_isr()
{
    if (g_hx711_instance != nullptr) {
        g_hx711_instance->handleInterrupt();
    }
}

// Global instance pointer definition
HX711* g_hx711_instance = nullptr;
